document.addEventListener('DOMContentLoaded', () => {
    const taskInput = document.getElementById('taskInput');
    const addTaskBtn = document.getElementById('addTaskBtn');
    const taskList = document.getElementById('taskList');

    // 从本地存储加载任务
    loadTasks();

    // 添加任务
    addTaskBtn.addEventListener('click', addTask);
    taskInput.addEventListener('keypress', (e) => {
        if (e.key === 'Enter') {
            addTask();
        }
    });

    function addTask() {
        const taskText = taskInput.value.trim();
        if (taskText === '') return;

        const task = {
            id: Date.now(),
            text: taskText,
            completed: false
        };

        saveTask(task);
        renderTask(task);
        taskInput.value = '';
    }

    function renderTask(task) {
        const li = document.createElement('li');
        li.dataset.id = task.id;
        if (task.completed) {
            li.classList.add('completed');
        }

        const taskText = document.createElement('span');
        taskText.textContent = task.text;

        const actions = document.createElement('div');
        const completeBtn = document.createElement('button');
        completeBtn.textContent = task.completed ? '未完成' : '完成';
        completeBtn.addEventListener('click', () => toggleTaskCompletion(task.id));

        const deleteBtn = document.createElement('button');
        deleteBtn.textContent = '删除';
        deleteBtn.addEventListener('click', () => deleteTask(task.id));

        actions.appendChild(completeBtn);
        actions.appendChild(deleteBtn);
        li.appendChild(taskText);
        li.appendChild(actions);
        taskList.appendChild(li);
    }

    function toggleTaskCompletion(id) {
        const tasks = getTasks();
        const taskIndex = tasks.findIndex(task => task.id === id);
        if (taskIndex !== -1) {
            tasks[taskIndex].completed = !tasks[taskIndex].completed;
            localStorage.setItem('tasks', JSON.stringify(tasks));
            const li = document.querySelector(`li[data-id="${id}"]`);
            li.classList.toggle('completed');
            const completeBtn = li.querySelector('div > button:first-child');
            completeBtn.textContent = tasks[taskIndex].completed ? '未完成' : '完成';
        }
    }

    function deleteTask(id) {
        let tasks = getTasks();
        tasks = tasks.filter(task => task.id !== id);
        localStorage.setItem('tasks', JSON.stringify(tasks));
        const li = document.querySelector(`li[data-id="${id}"]`);
        li.remove();
    }

    function saveTask(task) {
        const tasks = getTasks();
        tasks.push(task);
        localStorage.setItem('tasks', JSON.stringify(tasks));
    }

    function loadTasks() {
        const tasks = getTasks();
        tasks.forEach(task => renderTask(task));
    }

    function getTasks() {
        const tasks = localStorage.getItem('tasks');
        return tasks ? JSON.parse(tasks) : [];
    }
});