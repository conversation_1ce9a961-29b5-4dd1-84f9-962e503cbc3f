const { test, expect } = require('@playwright/test');

test('添加任务', async ({ page }) => {
  await page.goto('http://localhost:8000');
  await page.fill('#taskInput', '测试任务1');
  await page.click('#addTaskBtn');
  const taskText = await page.textContent('#taskList li:first-child span');
  expect(taskText).toBe('测试任务1');
});

test('标记任务为完成', async ({ page }) => {
  await page.goto('http://localhost:8000');
  await page.fill('#taskInput', '测试任务2');
  await page.click('#addTaskBtn');
  await page.click('#taskList li:first-child div > button:first-child');
  const isCompleted = await page.$eval('#taskList li:first-child', el => el.classList.contains('completed'));
  expect(isCompleted).toBe(true);
});

test('删除任务', async ({ page }) => {
  await page.goto('http://localhost:8000');
  await page.fill('#taskInput', '测试任务3');
  await page.click('#addTaskBtn');
  await page.click('#taskList li:first-child div > button:last-child');
  const taskCount = await page.$$eval('#taskList li', items => items.length);
  expect(taskCount).toBe(0);
});